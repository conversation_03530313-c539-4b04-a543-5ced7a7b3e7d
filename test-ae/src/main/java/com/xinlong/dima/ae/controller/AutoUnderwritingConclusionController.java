package com.xinlong.dima.ae.controller;

import com.xinlong.dima.ae.dto.Response;
import com.xinlong.dima.ae.req.AutoUnderwritingConclusionReq;
import com.xinlong.dima.ae.service.AutoUnderwritingConclusionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 自动核保结论控制器
 * <AUTHOR>
 * @date 2025/01/15
 * @description 基于program_design.md 4.1.2章节需求实现
 */
@Slf4j
@RestController
@RequestMapping("/znyf/auto_underwriting_conclusion")
public class AutoUnderwritingConclusionController {

    @Autowired
    private AutoUnderwritingConclusionService autoUnderwritingConclusionService;

    /**
     * 提交核保结论(FN0001)
     * 
     * 接口功能概述：提交核保结论
     * Method: POST
     * Content-Type: application/json
     * URL: /znyf/auto_underwriting_conclusion/edit
     * 
     * @param request 核保结论请求参数
     * @return 统一响应结果
     */
    @PostMapping("/edit")
    public Response<Void> submitUnderwritingConclusion(@Validated @RequestBody AutoUnderwritingConclusionReq request) {
        log.info("接收到提交核保结论请求，参数：{}", request);
        
        try {
            // 调用服务层处理业务逻辑
            autoUnderwritingConclusionService.submitUnderwritingConclusion(request);
            
            // 返回成功响应
            return Response.success(null);
            
        } catch (IllegalArgumentException e) {
            // 参数校验异常
            log.warn("提交核保结论参数校验失败：{}", e.getMessage());
            return Response.fail(400, e.getMessage());
            
        } catch (Exception e) {
            // 系统异常
            log.error("提交核保结论系统异常：{}", e.getMessage(), e);
            return Response.fail(500, "系统异常,请联系管理员!");
        }
    }
}
