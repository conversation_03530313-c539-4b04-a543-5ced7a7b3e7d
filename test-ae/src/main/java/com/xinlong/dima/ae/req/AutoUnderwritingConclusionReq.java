package com.xinlong.dima.ae.req;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 自动核保结论提交请求参数
 * <AUTHOR>
 * @date 2025/01/15
 * @description 基于program_design.md 4.1.2章节需求实现
 */
@Data
public class AutoUnderwritingConclusionReq {

    /**
     * 主键ID
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 核保结论名称
     */
    @NotBlank(message = "核保结论名称不能为空")
    private String name;

    /**
     * 核保结论
     */
    @NotBlank(message = "核保结论不能为空")
    private String underwritingConclusion;

    /**
     * 核保结论日期
     */
    @NotNull(message = "核保结论日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date underwritingDate;

    /**
     * 核保流向
     */
    @NotBlank(message = "核保流向不能为空")
    private String underwritingFlow;

    /**
     * 拒保原因
     */
    @NotBlank(message = "拒保原因不能为空")
    private String underwritingRejectionReason;
}
