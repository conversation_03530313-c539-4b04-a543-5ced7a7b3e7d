package com.xinlong.dima.ae.service;

import com.xinlong.dima.ae.req.AutoUnderwritingConclusionReq;

/**
 * 自动核保结论服务接口
 * <AUTHOR>
 * @date 2025/01/15
 * @description 基于program_design.md 4.1.2章节需求实现
 */
public interface AutoUnderwritingConclusionService {

    /**
     * 提交核保结论
     * 根据id更新TB0001表(t_4315_au_auto_underwriting_conclusion)
     * 
     * @param request 核保结论请求参数
     * @throws Exception 业务异常
     */
    void submitUnderwritingConclusion(AutoUnderwritingConclusionReq request) throws Exception;
}
