package com.xinlong.dima.ae.service.impl;

import com.jd.jdt.app4s.component.common.db.service.CommonModelService;
import com.xinlong.dima.ae.req.AutoUnderwritingConclusionReq;
import com.xinlong.dima.ae.service.AutoUnderwritingConclusionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * 自动核保结论服务实现
 * <AUTHOR>
 * @date 2025/01/15
 * @description 基于program_design.md 4.1.2章节需求实现
 */
@Slf4j
@Service
public class AutoUnderwritingConclusionServiceImpl implements AutoUnderwritingConclusionService {

    @Autowired
    private CommonModelService commonModelService;

    /**
     * 提交核保结论
     * 接收请求参数，并通过id更新TB0001表
     */
    @Override
    public void submitUnderwritingConclusion(AutoUnderwritingConclusionReq request) throws Exception {
        log.info("开始提交核保结论，请求参数：{}", request);
        
        try {
            // 参数校验
            validateRequest(request);
            
            // 构建更新数据
            Map<String, Object> updateData = buildUpdateData(request);
            
            // 执行数据库更新操作 - 对应TB0001表(t_4315_au_auto_underwriting_conclusion)
            int updateCount = commonModelService.updateById("auto_underwriting_conclusion", updateData);
            
            if (updateCount <= 0) {
                throw new RuntimeException("更新核保结论失败，未找到对应记录，id=" + request.getId());
            }
            
            log.info("核保结论提交成功，id={}", request.getId());
            
        } catch (Exception e) {
            log.error("提交核保结论异常，请求参数：{}，异常信息：{}", request, e.getMessage(), e);
            throw new Exception("提交核保结论失败：" + e.getMessage());
        }
    }

    /**
     * 参数校验
     */
    private void validateRequest(AutoUnderwritingConclusionReq request) {
        if (request.getId() == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (!StringUtils.hasText(request.getName())) {
            throw new IllegalArgumentException("核保结论名称不能为空");
        }
        if (!StringUtils.hasText(request.getUnderwritingConclusion())) {
            throw new IllegalArgumentException("核保结论不能为空");
        }
        if (request.getUnderwritingDate() == null) {
            throw new IllegalArgumentException("核保结论日期不能为空");
        }
        if (!StringUtils.hasText(request.getUnderwritingFlow())) {
            throw new IllegalArgumentException("核保流向不能为空");
        }
        if (!StringUtils.hasText(request.getUnderwritingRejectionReason())) {
            throw new IllegalArgumentException("拒保原因不能为空");
        }
    }

    /**
     * 构建更新数据
     */
    private Map<String, Object> buildUpdateData(AutoUnderwritingConclusionReq request) {
        Map<String, Object> updateData = new HashMap<>();
        
        // 设置主键ID
        updateData.put("id", request.getId());
        
        // 设置业务字段
        updateData.put("name", request.getName());
        updateData.put("underwriting_conclusion", request.getUnderwritingConclusion());
        updateData.put("underwriting_flow", request.getUnderwritingFlow());
        updateData.put("underwriting_rejection_reason", request.getUnderwritingRejectionReason());
        
        // 格式化日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        updateData.put("underwriting_date", dateFormat.format(request.getUnderwritingDate()));
        
        return updateData;
    }
}
